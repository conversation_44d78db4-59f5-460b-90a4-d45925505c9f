import { useState, useEffect } from 'react'

interface MoneyManagementStatusProps {
  className?: string
}

const MoneyManagementStatus: React.FC<MoneyManagementStatusProps> = ({ className = '' }) => {
  const [status, setStatus] = useState<MoneyManagementStatus | null>(null)
  const [accountBalance, setAccountBalance] = useState<number>(0)

  useEffect(() => {
    // Fetch initial status
    const fetchStatus = async (): Promise<void> => {
      try {
        const [mmStatus, balance] = await Promise.all([
          window.api.invoke('money-management:getStatus'),
          window.api.invoke('money-management:getAccountBalance')
        ])
        setStatus(mmStatus as MoneyManagementStatus | null)
        setAccountBalance(balance as number)
      } catch (error) {
        console.error('Failed to fetch money management status:', error)
      }
    }

    fetchStatus()

    // Listen for status updates
    const unsubscribe = window.api.on('broker:event', (...args: unknown[]) => {
      const [event, data] = args

      if (event === 'money-management:status') {
        console.log(`Money management status updated: ${JSON.stringify(data)}`)
        setStatus(data as MoneyManagementStatus)
      } else if (event === 'balance:updated') {
        console.log(`Account balance updated: ${JSON.stringify(data)}`)
        const balanceData = data as { balance: number }
        setAccountBalance(balanceData.balance)
      }
    })

    return unsubscribe
  }, [])

  if (!status || !status.isSessionActive) {
    return (
      <div className={`bg-gray-800/80 p-4 rounded-sm shadow-md ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-white text-lg font-bold">💰 Money Management</h3>
          <div className="px-2 py-1 bg-gray-700 rounded-full">
            <span className="text-xs text-gray-400" style={{ lineHeight: `14px` }}>
              Inactive
            </span>
          </div>
        </div>

        <div className="text-center py-6">
          <div className="text-gray-400 mb-3">
            <svg
              className="w-12 h-12 mx-auto mb-2 opacity-50"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
            No active trading session
          </div>
        </div>

        <div className="bg-gray-700/50 p-3 rounded border-l-4 border-blue-500">
          <div className="flex justify-between items-center">
            <span className="text-gray-300 text-sm">Account Balance</span>
            <span className="font-bold text-green-400 text-lg">${accountBalance.toFixed(2)}</span>
          </div>
        </div>
      </div>
    )
  }

  const profitPercentage =
    status.initialCapital > 0 ? (status.currentProfit / status.initialCapital) * 100 : 0
  const capitalPercentage =
    status.initialCapital > 0 ? (status.currentCapital / status.initialCapital) * 100 : 0
  const targetPercentage =
    status.targetProfit > 0 ? (status.currentProfit / status.targetProfit) * 100 : 0

  const getProgressColor = (percentage: number, isProfit = false): string => {
    if (isProfit) {
      if (percentage >= 100) return 'bg-green-500'
      if (percentage >= 50) return 'bg-green-400'
      if (percentage >= 0) return 'bg-yellow-400'
      return 'bg-red-400'
    }
    if (percentage >= 80) return 'bg-green-500'
    if (percentage >= 50) return 'bg-yellow-400'
    if (percentage >= 20) return 'bg-orange-400'
    return 'bg-red-500'
  }

  return (
    <div className={`bg-gray-800/80 p-4 rounded-sm shadow-md min-h-[500px] h-full ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-white text-lg font-bold">💰 Money Management</h3>
        <div className="px-2 py-1 bg-green-600 rounded-sm">
          <span className="text-xs text-white font-medium" style={{ lineHeight: `14px` }}>
            Active
          </span>
        </div>
      </div>

      {/* Key Metrics Row */}
      <div className="grid grid-cols-3 gap-3 mb-6">
        <div className="bg-gray-700/50 p-3 rounded text-center border-l-4 border-green-500">
          <div className="text-xs text-gray-400 mb-1">Current P/L</div>
          <div
            className={`font-bold text-lg ${status.currentProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}
          >
            ${status.currentProfit >= 0 ? '+' : ''}${status.currentProfit.toFixed(2)}
          </div>
          <div className="text-xs text-gray-400">
            {profitPercentage >= 0 ? '+' : ''}
            {profitPercentage.toFixed(1)}%
          </div>
        </div>

        <div className="bg-gray-700/50 p-3 rounded text-center border-l-4 border-blue-500">
          <div className="text-xs text-gray-400 mb-1">Capital Left</div>
          <div className="font-bold text-lg text-white">${status.currentCapital.toFixed(2)}</div>
          <div className="text-xs text-gray-400">{capitalPercentage.toFixed(1)}%</div>
        </div>

        <div className="bg-gray-700/50 p-3 rounded text-center border-l-4 border-purple-500">
          <div className="text-xs text-gray-400 mb-1">Next Trade</div>
          <div className="font-bold text-lg text-blue-400">
            ${status.nextTradeAmount.toFixed(2)}
          </div>
          <div className="text-xs text-gray-400">Amount</div>
        </div>
      </div>

      {/* Target Progress */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-300 text-sm font-medium">🎯 Target Progress</span>
          <span className="text-white font-bold">
            ${status.currentProfit.toFixed(2)} / ${status.targetProfit.toFixed(2)}
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-3">
          <div
            className="h-3 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full transition-all duration-500 relative"
            style={{ width: `${Math.min(Math.max(targetPercentage, 0), 100)}%` }}
          >
            {targetPercentage > 10 && (
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs text-white font-medium">
                  {targetPercentage.toFixed(0)}%
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="text-xs text-gray-400 mt-1">
          {targetPercentage.toFixed(1)}% of target reached
        </div>
      </div>

      {/* Trading Statistics */}
      <div className="mb-4">
        <h4 className="text-gray-300 text-sm font-medium mb-3">📊 Trading Statistics</h4>
        <div className="grid grid-cols-4 gap-2">
          <div className="bg-gray-700/50 p-3 rounded text-center">
            <div className="text-xs text-gray-400 mb-1">Total</div>
            <div className="font-bold text-white text-lg">{status.totalTrades}</div>
            <div className="text-xs text-gray-400">Trades</div>
          </div>
          <div className="bg-gray-700/50 p-3 rounded text-center">
            <div className="text-xs text-gray-400 mb-1">Success</div>
            <div
              className={`font-bold text-lg ${status.successRate >= 60 ? 'text-green-400' : status.successRate >= 40 ? 'text-yellow-400' : 'text-red-400'}`}
            >
              {status.successRate.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-400">Rate</div>
          </div>
          <div className="bg-gray-700/50 p-3 rounded text-center">
            <div className="text-xs text-gray-400 mb-1">Wins</div>
            <div className="font-bold text-green-400 text-lg">{status.winCount}</div>
            <div className="text-xs text-gray-400">Count</div>
          </div>
          <div className="bg-gray-700/50 p-3 rounded text-center">
            <div className="text-xs text-gray-400 mb-1">Losses</div>
            <div className="font-bold text-red-400 text-lg">{status.lossCount}</div>
            <div className="text-xs text-gray-400">Count</div>
          </div>
        </div>
      </div>

      {/* Capital Health Bar */}
      <div className="bg-gray-700/30 p-3 rounded border border-gray-600">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-300 text-sm font-medium">💪 Capital Health</span>
          <span className="text-white font-bold">${status.currentCapital.toFixed(2)}</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(capitalPercentage)}`}
            style={{ width: `${Math.max(capitalPercentage, 0)}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-400 mt-1">
          {capitalPercentage.toFixed(1)}% of initial capital remaining
        </div>
      </div>
    </div>
  )
}

export default MoneyManagementStatus
